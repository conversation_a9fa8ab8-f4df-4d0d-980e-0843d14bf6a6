﻿namespace QuanLyBanGiay.View.VMain
{
    partial class frmMain
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmMain));
            this.barButtonItem5 = new DevExpress.XtraBars.BarButtonItem();
            this.defaultLookAndFeel1 = new DevExpress.LookAndFeel.DefaultLookAndFeel();
            this.ribbonPage2 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.barButtonItem8 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem13 = new DevExpress.XtraBars.BarButtonItem();
            this.xtraTabbedMdiManager1 = new DevExpress.XtraTabbedMdi.XtraTabbedMdiManager();
            this.barButtonItem23 = new DevExpress.XtraBars.BarButtonItem();
            this.mnuTroGiup = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup7 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.btnHuongDan = new DevExpress.XtraBars.BarButtonItem();
            this.btnDieuKhoan = new DevExpress.XtraBars.BarButtonItem();
            this.ribbonPageGroup8 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.btnBanQuyen = new DevExpress.XtraBars.BarButtonItem();
            this.mnuThongKe = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup1 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.btnThongKe = new DevExpress.XtraBars.BarButtonItem();
            this.mnuQuanLyNhanVien = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup4 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.btnNhanVien = new DevExpress.XtraBars.BarButtonItem();
            this.ribbonPageGroup9 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.barButtonItem14 = new DevExpress.XtraBars.BarButtonItem();
            this.menuQLKH = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup5 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.btnKhachHang = new DevExpress.XtraBars.BarButtonItem();
            this.mnuQuanLyHoaDon = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup6 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.btnPhieuNhap = new DevExpress.XtraBars.BarButtonItem();
            this.btnChiTietPhieuNhap = new DevExpress.XtraBars.BarButtonItem();
            this.ribbonPageGroup3 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.btnPhieuXuat = new DevExpress.XtraBars.BarButtonItem();
            this.btnChiTietPhieuXuat = new DevExpress.XtraBars.BarButtonItem();
            this.menuQuanLySanPham = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.grQuanLySanPham = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.btnSanPham = new DevExpress.XtraBars.BarButtonItem();
            this.btnLoaiGiay = new DevExpress.XtraBars.BarButtonItem();
            this.btnNhaSanXuat = new DevExpress.XtraBars.BarButtonItem();
            this.btnNhaCungCap = new DevExpress.XtraBars.BarButtonItem();
            this.menuHethong = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup2 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.btnLogin = new DevExpress.XtraBars.BarButtonItem();
            this.btnLogout = new DevExpress.XtraBars.BarButtonItem();
            this.btnSQL = new DevExpress.XtraBars.BarButtonItem();
            this.btnDoiMatKhau = new DevExpress.XtraBars.BarButtonItem();
            this.btnCaiDat = new DevExpress.XtraBars.BarButtonItem();
            this.btnTaiKhoan = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem10 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem11 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem12 = new DevExpress.XtraBars.BarButtonItem();
            this.btnPQ = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem16 = new DevExpress.XtraBars.BarButtonItem();
            this.btnSetting = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem18 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem19 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem6 = new DevExpress.XtraBars.BarButtonItem();
            this.barMonHoc = new DevExpress.XtraBars.BarButtonItem();
            this.barChuNhiem = new DevExpress.XtraBars.BarButtonItem();
            this.barDiemHS = new DevExpress.XtraBars.BarButtonItem();
            this.btnThongkeDiem = new DevExpress.XtraBars.BarButtonItem();
            this.btnThongKeGV = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem22 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem27 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem30 = new DevExpress.XtraBars.BarButtonItem();
            this.ribbon = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.barButtonItem7 = new DevExpress.XtraBars.BarButtonItem();
            this.ribbonStatusBar = new DevExpress.XtraBars.Ribbon.RibbonStatusBar();
            this.MdiManager = new DevExpress.XtraTabbedMdi.XtraTabbedMdiManager();
            this.barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem4 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem3 = new DevExpress.XtraBars.BarButtonItem();
            this.btnPhanQuyen = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem9 = new DevExpress.XtraBars.BarButtonItem();
            this.ribbonPageGroup10 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabbedMdiManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.MdiManager)).BeginInit();
            this.SuspendLayout();
            // 
            // barButtonItem5
            // 
            this.barButtonItem5.Caption = "Kết nối cơ sở dữ liệu";
            this.barButtonItem5.Id = 62;
            this.barButtonItem5.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem5.ImageOptions.Image")));
            this.barButtonItem5.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem5.ImageOptions.LargeImage")));
            this.barButtonItem5.Name = "barButtonItem5";
            // 
            // defaultLookAndFeel1
            // 
            this.defaultLookAndFeel1.LookAndFeel.SkinName = "Office 2016 Colorful";
            // 
            // ribbonPage2
            // 
            this.ribbonPage2.Name = "ribbonPage2";
            this.ribbonPage2.Text = "ribbonPage2";
            // 
            // barButtonItem8
            // 
            this.barButtonItem8.Caption = "Học Sinh";
            this.barButtonItem8.Id = 5;
            this.barButtonItem8.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem8.ImageOptions.LargeImage")));
            this.barButtonItem8.Name = "barButtonItem8";
            // 
            // barButtonItem13
            // 
            this.barButtonItem13.Caption = "LognOut";
            this.barButtonItem13.Id = 2;
            this.barButtonItem13.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem13.ImageOptions.Image")));
            this.barButtonItem13.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem13.ImageOptions.LargeImage")));
            this.barButtonItem13.Name = "barButtonItem13";
            // 
            // xtraTabbedMdiManager1
            // 
            this.xtraTabbedMdiManager1.MdiParent = this;
            // 
            // barButtonItem23
            // 
            this.barButtonItem23.Caption = "Chi Tiết Phiếu Nhập";
            this.barButtonItem23.Id = 30;
            this.barButtonItem23.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem23.ImageOptions.Image")));
            this.barButtonItem23.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem23.ImageOptions.LargeImage")));
            this.barButtonItem23.Name = "barButtonItem23";
            // 
            // mnuTroGiup
            // 
            this.mnuTroGiup.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup7,
            this.ribbonPageGroup8});
            this.mnuTroGiup.Name = "mnuTroGiup";
            this.mnuTroGiup.Text = "Trợ giúp";
            // 
            // ribbonPageGroup7
            // 
            this.ribbonPageGroup7.ItemLinks.Add(this.btnHuongDan);
            this.ribbonPageGroup7.ItemLinks.Add(this.btnDieuKhoan);
            this.ribbonPageGroup7.Name = "ribbonPageGroup7";
            // 
            // btnHuongDan
            // 
            this.btnHuongDan.Caption = "Hướng dẫn";
            this.btnHuongDan.Id = 35;
            this.btnHuongDan.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnHuongDan.ImageOptions.Image")));
            this.btnHuongDan.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnHuongDan.ImageOptions.LargeImage")));
            this.btnHuongDan.Name = "btnHuongDan";
            this.btnHuongDan.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnHuongDan_ItemClick);
            // 
            // btnDieuKhoan
            // 
            this.btnDieuKhoan.Caption = "Điều khoản";
            this.btnDieuKhoan.Id = 36;
            this.btnDieuKhoan.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnDieuKhoan.ImageOptions.Image")));
            this.btnDieuKhoan.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnDieuKhoan.ImageOptions.LargeImage")));
            this.btnDieuKhoan.Name = "btnDieuKhoan";
            this.btnDieuKhoan.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnDieuKhoan_ItemClick);
            // 
            // ribbonPageGroup8
            // 
            this.ribbonPageGroup8.ItemLinks.Add(this.btnBanQuyen);
            this.ribbonPageGroup8.Name = "ribbonPageGroup8";
            // 
            // btnBanQuyen
            // 
            this.btnBanQuyen.Caption = "Bản quyền";
            this.btnBanQuyen.Id = 38;
            this.btnBanQuyen.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnBanQuyen.ImageOptions.Image")));
            this.btnBanQuyen.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnBanQuyen.ImageOptions.LargeImage")));
            this.btnBanQuyen.Name = "btnBanQuyen";
            this.btnBanQuyen.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnBanQuyen_ItemClick);
            // 
            // mnuThongKe
            // 
            this.mnuThongKe.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup1});
            this.mnuThongKe.Name = "mnuThongKe";
            this.mnuThongKe.Text = "Thống kê";
            // 
            // ribbonPageGroup1
            // 
            this.ribbonPageGroup1.ItemLinks.Add(this.btnThongKe);
            this.ribbonPageGroup1.Name = "ribbonPageGroup1";
            // 
            // btnThongKe
            // 
            this.btnThongKe.Caption = "Thống kê";
            this.btnThongKe.Id = 25;
            this.btnThongKe.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnThongKe.ImageOptions.Image")));
            this.btnThongKe.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnThongKe.ImageOptions.LargeImage")));
            this.btnThongKe.Name = "btnThongKe";
            this.btnThongKe.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnThongKe_ItemClick);
            // 
            // mnuQuanLyNhanVien
            // 
            this.mnuQuanLyNhanVien.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup4,
            this.ribbonPageGroup9});
            this.mnuQuanLyNhanVien.Name = "mnuQuanLyNhanVien";
            this.mnuQuanLyNhanVien.Text = "Quản lý nhân viên";
            // 
            // ribbonPageGroup4
            // 
            this.ribbonPageGroup4.ItemLinks.Add(this.btnNhanVien);
            this.ribbonPageGroup4.Name = "ribbonPageGroup4";
            // 
            // btnNhanVien
            // 
            this.btnNhanVien.Caption = "Quản lý danh sách nhân vên";
            this.btnNhanVien.Id = 26;
            this.btnNhanVien.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnNhanVien.ImageOptions.Image")));
            this.btnNhanVien.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnNhanVien.ImageOptions.LargeImage")));
            this.btnNhanVien.Name = "btnNhanVien";
            this.btnNhanVien.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnNhanVien_ItemClick);
            // 
            // ribbonPageGroup9
            // 
            this.ribbonPageGroup9.ItemLinks.Add(this.barButtonItem14);
            this.ribbonPageGroup9.Name = "ribbonPageGroup9";
            // 
            // barButtonItem14
            // 
            this.barButtonItem14.Caption = "Danh sách tài khoản";
            this.barButtonItem14.Id = 40;
            this.barButtonItem14.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem14.ImageOptions.Image")));
            this.barButtonItem14.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem14.ImageOptions.LargeImage")));
            this.barButtonItem14.Name = "barButtonItem14";
            this.barButtonItem14.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem14_ItemClick);
            // 
            // menuQLKH
            // 
            this.menuQLKH.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup5});
            this.menuQLKH.Name = "menuQLKH";
            this.menuQLKH.Text = "Quản lý khách hàng";
            // 
            // ribbonPageGroup5
            // 
            this.ribbonPageGroup5.ItemLinks.Add(this.btnKhachHang);
            this.ribbonPageGroup5.Name = "ribbonPageGroup5";
            // 
            // btnKhachHang
            // 
            this.btnKhachHang.Caption = "Quản lý danh sách khách hàng";
            this.btnKhachHang.Id = 33;
            this.btnKhachHang.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnKhachHang.ImageOptions.Image")));
            this.btnKhachHang.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnKhachHang.ImageOptions.LargeImage")));
            this.btnKhachHang.Name = "btnKhachHang";
            this.btnKhachHang.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnKhachHang_ItemClick);
            // 
            // mnuQuanLyHoaDon
            // 
            this.mnuQuanLyHoaDon.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup6,
            this.ribbonPageGroup3});
            this.mnuQuanLyHoaDon.Name = "mnuQuanLyHoaDon";
            this.mnuQuanLyHoaDon.Text = "Quản lý hóa đơn";
            // 
            // ribbonPageGroup6
            // 
            this.ribbonPageGroup6.ItemLinks.Add(this.btnPhieuNhap);
            this.ribbonPageGroup6.ItemLinks.Add(this.btnChiTietPhieuNhap);
            this.ribbonPageGroup6.Name = "ribbonPageGroup6";
            // 
            // btnPhieuNhap
            // 
            this.btnPhieuNhap.Caption = "Phiếu Nhập";
            this.btnPhieuNhap.Id = 27;
            this.btnPhieuNhap.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnPhieuNhap.ImageOptions.Image")));
            this.btnPhieuNhap.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnPhieuNhap.ImageOptions.LargeImage")));
            this.btnPhieuNhap.Name = "btnPhieuNhap";
            this.btnPhieuNhap.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnPhieuNhap_ItemClick);
            // 
            // btnChiTietPhieuNhap
            // 
            this.btnChiTietPhieuNhap.Caption = "Chi Tiết Phiếu Nhập";
            this.btnChiTietPhieuNhap.Id = 28;
            this.btnChiTietPhieuNhap.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnChiTietPhieuNhap.ImageOptions.Image")));
            this.btnChiTietPhieuNhap.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnChiTietPhieuNhap.ImageOptions.LargeImage")));
            this.btnChiTietPhieuNhap.Name = "btnChiTietPhieuNhap";
            this.btnChiTietPhieuNhap.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnChiTietPhieuNhap_ItemClick);
            // 
            // ribbonPageGroup3
            // 
            this.ribbonPageGroup3.ItemLinks.Add(this.btnPhieuXuat);
            this.ribbonPageGroup3.ItemLinks.Add(this.btnChiTietPhieuXuat);
            this.ribbonPageGroup3.Name = "ribbonPageGroup3";
            // 
            // btnPhieuXuat
            // 
            this.btnPhieuXuat.Caption = "Phiếu Xuất";
            this.btnPhieuXuat.Id = 31;
            this.btnPhieuXuat.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnPhieuXuat.ImageOptions.Image")));
            this.btnPhieuXuat.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnPhieuXuat.ImageOptions.LargeImage")));
            this.btnPhieuXuat.Name = "btnPhieuXuat";
            this.btnPhieuXuat.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnPhieuXuat_ItemClick);
            // 
            // btnChiTietPhieuXuat
            // 
            this.btnChiTietPhieuXuat.Caption = "Chi Tiết Phiếu Xuất";
            this.btnChiTietPhieuXuat.Id = 32;
            this.btnChiTietPhieuXuat.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnChiTietPhieuXuat.ImageOptions.Image")));
            this.btnChiTietPhieuXuat.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnChiTietPhieuXuat.ImageOptions.LargeImage")));
            this.btnChiTietPhieuXuat.Name = "btnChiTietPhieuXuat";
            this.btnChiTietPhieuXuat.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnChiTietPhieuXuat_ItemClick);
            // 
            // menuQuanLySanPham
            // 
            this.menuQuanLySanPham.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.grQuanLySanPham});
            this.menuQuanLySanPham.Name = "menuQuanLySanPham";
            this.menuQuanLySanPham.Text = "Quản lý sản phẩm";
            // 
            // grQuanLySanPham
            // 
            this.grQuanLySanPham.ItemLinks.Add(this.btnSanPham);
            this.grQuanLySanPham.ItemLinks.Add(this.btnLoaiGiay);
            this.grQuanLySanPham.ItemLinks.Add(this.btnNhaSanXuat);
            this.grQuanLySanPham.ItemLinks.Add(this.btnNhaCungCap);
            this.grQuanLySanPham.Name = "grQuanLySanPham";
            this.grQuanLySanPham.Text = "Quản lý sản phẩm";
            // 
            // btnSanPham
            // 
            this.btnSanPham.Caption = "Sản Phẩm";
            this.btnSanPham.Id = 4;
            this.btnSanPham.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnSanPham.ImageOptions.Image")));
            this.btnSanPham.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnSanPham.ImageOptions.LargeImage")));
            this.btnSanPham.Name = "btnSanPham";
            this.btnSanPham.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnSanPham_ItemClick);
            // 
            // btnLoaiGiay
            // 
            this.btnLoaiGiay.Caption = "Loại giày";
            this.btnLoaiGiay.Id = 5;
            this.btnLoaiGiay.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnLoaiGiay.ImageOptions.Image")));
            this.btnLoaiGiay.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnLoaiGiay.ImageOptions.LargeImage")));
            this.btnLoaiGiay.Name = "btnLoaiGiay";
            this.btnLoaiGiay.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnLoaiGiay_ItemClick);
            // 
            // btnNhaSanXuat
            // 
            this.btnNhaSanXuat.Caption = "Nhà sản xuất";
            this.btnNhaSanXuat.Id = 6;
            this.btnNhaSanXuat.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnNhaSanXuat.ImageOptions.Image")));
            this.btnNhaSanXuat.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnNhaSanXuat.ImageOptions.LargeImage")));
            this.btnNhaSanXuat.Name = "btnNhaSanXuat";
            this.btnNhaSanXuat.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barNhaSanXuat_ItemClick);
            // 
            // btnNhaCungCap
            // 
            this.btnNhaCungCap.Caption = "Nhà cung cấp";
            this.btnNhaCungCap.Id = 7;
            this.btnNhaCungCap.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnNhaCungCap.ImageOptions.Image")));
            this.btnNhaCungCap.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnNhaCungCap.ImageOptions.LargeImage")));
            this.btnNhaCungCap.Name = "btnNhaCungCap";
            this.btnNhaCungCap.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barNhaCungCap_ItemClick);
            // 
            // menuHethong
            // 
            this.menuHethong.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup2});
            this.menuHethong.Name = "menuHethong";
            this.menuHethong.Text = "Hệ Thống";
            // 
            // ribbonPageGroup2
            // 
            this.ribbonPageGroup2.ItemLinks.Add(this.btnLogin);
            this.ribbonPageGroup2.ItemLinks.Add(this.btnLogout);
            this.ribbonPageGroup2.ItemLinks.Add(this.btnSQL);
            this.ribbonPageGroup2.ItemLinks.Add(this.btnDoiMatKhau);
            this.ribbonPageGroup2.ItemLinks.Add(this.btnCaiDat);
            this.ribbonPageGroup2.Name = "ribbonPageGroup2";
            this.ribbonPageGroup2.Text = "Hệ Thống";
            // 
            // btnLogin
            // 
            this.btnLogin.Caption = "Login";
            this.btnLogin.Id = 1;
            this.btnLogin.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnLogin.ImageOptions.Image")));
            this.btnLogin.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnLogin.ImageOptions.LargeImage")));
            this.btnLogin.Name = "btnLogin";
            this.btnLogin.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnLogin_ItemClick);
            // 
            // btnLogout
            // 
            this.btnLogout.Caption = "LognOut";
            this.btnLogout.Id = 2;
            this.btnLogout.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnLogout.ImageOptions.Image")));
            this.btnLogout.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnLogout.ImageOptions.LargeImage")));
            this.btnLogout.Name = "btnLogout";
            this.btnLogout.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnLogout_ItemClick);
            // 
            // btnSQL
            // 
            this.btnSQL.Caption = "Conect đến hệ thống";
            this.btnSQL.Id = 13;
            this.btnSQL.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnSQL.ImageOptions.LargeImage")));
            this.btnSQL.Name = "btnSQL";
            this.btnSQL.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnSQL_ItemClick);
            // 
            // btnDoiMatKhau
            // 
            this.btnDoiMatKhau.Caption = "đổi mật khẩu";
            this.btnDoiMatKhau.Id = 12;
            this.btnDoiMatKhau.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnDoiMatKhau.ImageOptions.Image")));
            this.btnDoiMatKhau.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnDoiMatKhau.ImageOptions.LargeImage")));
            this.btnDoiMatKhau.Name = "btnDoiMatKhau";
            // 
            // btnCaiDat
            // 
            this.btnCaiDat.Caption = "Cài Đặt";
            this.btnCaiDat.Id = 22;
            this.btnCaiDat.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnCaiDat.ImageOptions.Image")));
            this.btnCaiDat.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnCaiDat.ImageOptions.LargeImage")));
            this.btnCaiDat.Name = "btnCaiDat";
            // 
            // btnTaiKhoan
            // 
            this.btnTaiKhoan.Caption = "Tài Khoản";
            this.btnTaiKhoan.Id = 3;
            this.btnTaiKhoan.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnTaiKhoan.ImageOptions.LargeImage")));
            this.btnTaiKhoan.Name = "btnTaiKhoan";
            // 
            // barButtonItem10
            // 
            this.barButtonItem10.Caption = "gửi thông báo";
            this.barButtonItem10.Id = 8;
            this.barButtonItem10.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem10.ImageOptions.Image")));
            this.barButtonItem10.Name = "barButtonItem10";
            // 
            // barButtonItem11
            // 
            this.barButtonItem11.Caption = "Inport";
            this.barButtonItem11.Id = 9;
            this.barButtonItem11.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem11.ImageOptions.LargeImage")));
            this.barButtonItem11.Name = "barButtonItem11";
            // 
            // barButtonItem12
            // 
            this.barButtonItem12.Caption = "Export";
            this.barButtonItem12.Id = 10;
            this.barButtonItem12.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem12.ImageOptions.LargeImage")));
            this.barButtonItem12.Name = "barButtonItem12";
            // 
            // btnPQ
            // 
            this.btnPQ.Caption = "Phân Quyền";
            this.btnPQ.Id = 11;
            this.btnPQ.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnPQ.ImageOptions.Image")));
            this.btnPQ.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnPQ.ImageOptions.LargeImage")));
            this.btnPQ.Name = "btnPQ";
            // 
            // barButtonItem16
            // 
            this.barButtonItem16.Caption = "barButtonItem16";
            this.barButtonItem16.Id = 14;
            this.barButtonItem16.Name = "barButtonItem16";
            // 
            // btnSetting
            // 
            this.btnSetting.Caption = "Setting";
            this.btnSetting.Id = 15;
            this.btnSetting.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnSetting.ImageOptions.LargeImage")));
            this.btnSetting.Name = "btnSetting";
            // 
            // barButtonItem18
            // 
            this.barButtonItem18.Caption = "barButtonItem18";
            this.barButtonItem18.Id = 16;
            this.barButtonItem18.Name = "barButtonItem18";
            // 
            // barButtonItem19
            // 
            this.barButtonItem19.Caption = "gửi bảng điểm";
            this.barButtonItem19.Id = 17;
            this.barButtonItem19.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem19.ImageOptions.Image")));
            this.barButtonItem19.Name = "barButtonItem19";
            // 
            // barButtonItem6
            // 
            this.barButtonItem6.Caption = "barButtonItem6";
            this.barButtonItem6.Id = 18;
            this.barButtonItem6.Name = "barButtonItem6";
            // 
            // barMonHoc
            // 
            this.barMonHoc.Caption = "Môn Học";
            this.barMonHoc.Id = 19;
            this.barMonHoc.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barMonHoc.ImageOptions.Image")));
            this.barMonHoc.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barMonHoc.ImageOptions.LargeImage")));
            this.barMonHoc.Name = "barMonHoc";
            // 
            // barChuNhiem
            // 
            this.barChuNhiem.Caption = "Chủ nhiệm";
            this.barChuNhiem.Id = 20;
            this.barChuNhiem.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barChuNhiem.ImageOptions.Image")));
            this.barChuNhiem.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barChuNhiem.ImageOptions.LargeImage")));
            this.barChuNhiem.Name = "barChuNhiem";
            // 
            // barDiemHS
            // 
            this.barDiemHS.Caption = "Điểm Học Sinh";
            this.barDiemHS.Id = 21;
            this.barDiemHS.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barDiemHS.ImageOptions.Image")));
            this.barDiemHS.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barDiemHS.ImageOptions.LargeImage")));
            this.barDiemHS.Name = "barDiemHS";
            // 
            // btnThongkeDiem
            // 
            this.btnThongkeDiem.Caption = "Thống kê điểm học sinh";
            this.btnThongkeDiem.Id = 23;
            this.btnThongkeDiem.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnThongkeDiem.ImageOptions.Image")));
            this.btnThongkeDiem.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnThongkeDiem.ImageOptions.LargeImage")));
            this.btnThongkeDiem.Name = "btnThongkeDiem";
            // 
            // btnThongKeGV
            // 
            this.btnThongKeGV.Caption = "Thống kê Giáo viên";
            this.btnThongKeGV.Id = 24;
            this.btnThongKeGV.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnThongKeGV.ImageOptions.Image")));
            this.btnThongKeGV.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnThongKeGV.ImageOptions.LargeImage")));
            this.btnThongKeGV.Name = "btnThongKeGV";
            // 
            // barButtonItem22
            // 
            this.barButtonItem22.Caption = "Chi Tiết Phiếu Nhập";
            this.barButtonItem22.Id = 29;
            this.barButtonItem22.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem22.ImageOptions.Image")));
            this.barButtonItem22.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem22.ImageOptions.LargeImage")));
            this.barButtonItem22.Name = "barButtonItem22";
            // 
            // barButtonItem27
            // 
            this.barButtonItem27.Caption = "Quản lý danh sách nhân viên";
            this.barButtonItem27.Id = 34;
            this.barButtonItem27.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem27.ImageOptions.Image")));
            this.barButtonItem27.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem27.ImageOptions.LargeImage")));
            this.barButtonItem27.Name = "barButtonItem27";
            // 
            // barButtonItem30
            // 
            this.barButtonItem30.Caption = "barButtonItem30";
            this.barButtonItem30.Id = 37;
            this.barButtonItem30.Name = "barButtonItem30";
            // 
            // ribbon
            // 
            this.ribbon.ExpandCollapseItem.Id = 0;
            this.ribbon.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbon.ExpandCollapseItem,
            this.btnLogin,
            this.btnLogout,
            this.btnTaiKhoan,
            this.btnSanPham,
            this.btnLoaiGiay,
            this.btnNhaSanXuat,
            this.btnNhaCungCap,
            this.barButtonItem10,
            this.barButtonItem11,
            this.barButtonItem12,
            this.btnPQ,
            this.btnDoiMatKhau,
            this.btnSQL,
            this.barButtonItem16,
            this.btnSetting,
            this.barButtonItem18,
            this.barButtonItem19,
            this.barButtonItem6,
            this.barMonHoc,
            this.barChuNhiem,
            this.barDiemHS,
            this.btnCaiDat,
            this.btnThongkeDiem,
            this.btnThongKeGV,
            this.btnThongKe,
            this.btnNhanVien,
            this.btnPhieuNhap,
            this.btnChiTietPhieuNhap,
            this.barButtonItem22,
            this.btnPhieuXuat,
            this.btnChiTietPhieuXuat,
            this.btnKhachHang,
            this.barButtonItem27,
            this.btnHuongDan,
            this.btnDieuKhoan,
            this.barButtonItem30,
            this.btnBanQuyen,
            this.barButtonItem7,
            this.barButtonItem14});
            this.ribbon.Location = new System.Drawing.Point(0, 0);
            this.ribbon.MaxItemId = 41;
            this.ribbon.Name = "ribbon";
            this.ribbon.Pages.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPage[] {
            this.menuHethong,
            this.menuQuanLySanPham,
            this.mnuQuanLyHoaDon,
            this.menuQLKH,
            this.mnuQuanLyNhanVien,
            this.mnuThongKe,
            this.mnuTroGiup});
            this.ribbon.Size = new System.Drawing.Size(1348, 146);
            this.ribbon.StatusBar = this.ribbonStatusBar;
            // 
            // barButtonItem7
            // 
            this.barButtonItem7.Caption = "barButtonItem7";
            this.barButtonItem7.Id = 39;
            this.barButtonItem7.Name = "barButtonItem7";
            // 
            // ribbonStatusBar
            // 
            this.ribbonStatusBar.Location = new System.Drawing.Point(0, 742);
            this.ribbonStatusBar.Name = "ribbonStatusBar";
            this.ribbonStatusBar.Ribbon = this.ribbon;
            this.ribbonStatusBar.Size = new System.Drawing.Size(1348, 21);
            // 
            // MdiManager
            // 
            this.MdiManager.MdiParent = null;
            // 
            // barButtonItem2
            // 
            this.barButtonItem2.Caption = "Kết nối cơ sở dữ liệu";
            this.barButtonItem2.Id = 62;
            this.barButtonItem2.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem2.ImageOptions.Image")));
            this.barButtonItem2.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem2.ImageOptions.LargeImage")));
            this.barButtonItem2.Name = "barButtonItem2";
            // 
            // barButtonItem1
            // 
            this.barButtonItem1.Caption = "Phân Quyền";
            this.barButtonItem1.Id = 48;
            this.barButtonItem1.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem1.ImageOptions.Image")));
            this.barButtonItem1.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem1.ImageOptions.LargeImage")));
            this.barButtonItem1.Name = "barButtonItem1";
            // 
            // barButtonItem4
            // 
            this.barButtonItem4.Caption = "Kết nối cơ sở dữ liệu";
            this.barButtonItem4.Id = 62;
            this.barButtonItem4.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem4.ImageOptions.Image")));
            this.barButtonItem4.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem4.ImageOptions.LargeImage")));
            this.barButtonItem4.Name = "barButtonItem4";
            // 
            // barButtonItem3
            // 
            this.barButtonItem3.Caption = "Kết nối cơ sở dữ liệu";
            this.barButtonItem3.Id = 62;
            this.barButtonItem3.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem3.ImageOptions.Image")));
            this.barButtonItem3.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem3.ImageOptions.LargeImage")));
            this.barButtonItem3.Name = "barButtonItem3";
            // 
            // btnPhanQuyen
            // 
            this.btnPhanQuyen.Caption = "Phân Quyền";
            this.btnPhanQuyen.Id = 48;
            this.btnPhanQuyen.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("btnPhanQuyen.ImageOptions.Image")));
            this.btnPhanQuyen.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("btnPhanQuyen.ImageOptions.LargeImage")));
            this.btnPhanQuyen.Name = "btnPhanQuyen";
            // 
            // barButtonItem9
            // 
            this.barButtonItem9.Caption = "Học Sinh";
            this.barButtonItem9.Id = 5;
            this.barButtonItem9.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("barButtonItem9.ImageOptions.LargeImage")));
            this.barButtonItem9.Name = "barButtonItem9";
            // 
            // ribbonPageGroup10
            // 
            //this.ribbonPageGroup10.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("ribbonPageGroup10.ImageOptions.Image")));
            this.ribbonPageGroup10.ItemLinks.Add(this.barButtonItem7);
            this.ribbonPageGroup10.Name = "ribbonPageGroup10";
            this.ribbonPageGroup10.Text = "Quản lý danh sách tài khoản";
            // 
            // frmMain
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1348, 763);
            this.Controls.Add(this.ribbonStatusBar);
            this.Controls.Add(this.ribbon);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.IsMdiContainer = true;
            this.Name = "frmMain";
            this.Ribbon = this.ribbon;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.StatusBar = this.ribbonStatusBar;
            this.Text = "Chương trình quản lý cửa hàng bán giày";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frmMain_FormClosing);
            this.Load += new System.EventHandler(this.frmMain_Load);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabbedMdiManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.MdiManager)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarButtonItem barButtonItem5;
        private DevExpress.LookAndFeel.DefaultLookAndFeel defaultLookAndFeel1;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage2;
        private DevExpress.XtraBars.BarButtonItem barButtonItem8;
        private DevExpress.XtraBars.BarButtonItem barButtonItem13;
        private DevExpress.XtraTabbedMdi.XtraTabbedMdiManager xtraTabbedMdiManager1;
        private DevExpress.XtraBars.Ribbon.RibbonStatusBar ribbonStatusBar;
        private DevExpress.XtraBars.Ribbon.RibbonControl ribbon;
        private DevExpress.XtraBars.BarButtonItem btnLogin;
        private DevExpress.XtraBars.BarButtonItem btnLogout;
        private DevExpress.XtraBars.BarButtonItem btnTaiKhoan;
        private DevExpress.XtraBars.BarButtonItem btnSanPham;
        private DevExpress.XtraBars.BarButtonItem btnLoaiGiay;
        private DevExpress.XtraBars.BarButtonItem btnNhaSanXuat;
        private DevExpress.XtraBars.BarButtonItem btnNhaCungCap;
        private DevExpress.XtraBars.BarButtonItem barButtonItem10;
        private DevExpress.XtraBars.BarButtonItem barButtonItem11;
        private DevExpress.XtraBars.BarButtonItem barButtonItem12;
        private DevExpress.XtraBars.BarButtonItem btnPQ;
        private DevExpress.XtraBars.BarButtonItem btnDoiMatKhau;
        private DevExpress.XtraBars.BarButtonItem btnSQL;
        private DevExpress.XtraBars.BarButtonItem barButtonItem16;
        private DevExpress.XtraBars.BarButtonItem btnSetting;
        private DevExpress.XtraBars.BarButtonItem barButtonItem18;
        private DevExpress.XtraBars.BarButtonItem barButtonItem19;
        private DevExpress.XtraBars.BarButtonItem barButtonItem6;
        private DevExpress.XtraBars.BarButtonItem barMonHoc;
        private DevExpress.XtraBars.BarButtonItem barChuNhiem;
        private DevExpress.XtraBars.BarButtonItem barDiemHS;
        private DevExpress.XtraBars.BarButtonItem btnCaiDat;
        private DevExpress.XtraBars.BarButtonItem btnThongkeDiem;
        private DevExpress.XtraBars.BarButtonItem btnThongKeGV;
        private DevExpress.XtraBars.BarButtonItem btnThongKe;
        private DevExpress.XtraBars.BarButtonItem btnNhanVien;
        private DevExpress.XtraBars.BarButtonItem btnPhieuNhap;
        private DevExpress.XtraBars.BarButtonItem btnChiTietPhieuNhap;
        private DevExpress.XtraBars.BarButtonItem barButtonItem22;
        private DevExpress.XtraBars.BarButtonItem btnPhieuXuat;
        private DevExpress.XtraBars.BarButtonItem btnChiTietPhieuXuat;
        private DevExpress.XtraBars.BarButtonItem btnKhachHang;
        private DevExpress.XtraBars.BarButtonItem barButtonItem27;
        private DevExpress.XtraBars.BarButtonItem btnHuongDan;
        private DevExpress.XtraBars.BarButtonItem btnDieuKhoan;
        private DevExpress.XtraBars.BarButtonItem barButtonItem30;
        private DevExpress.XtraBars.BarButtonItem btnBanQuyen;
        private DevExpress.XtraBars.BarButtonItem barButtonItem7;
        private DevExpress.XtraBars.BarButtonItem barButtonItem14;
        private DevExpress.XtraBars.Ribbon.RibbonPage menuHethong;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup2;
        private DevExpress.XtraBars.Ribbon.RibbonPage menuQuanLySanPham;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup grQuanLySanPham;
        private DevExpress.XtraBars.Ribbon.RibbonPage mnuQuanLyHoaDon;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup6;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup3;
        private DevExpress.XtraBars.Ribbon.RibbonPage menuQLKH;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup5;
        private DevExpress.XtraBars.Ribbon.RibbonPage mnuQuanLyNhanVien;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup4;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup9;
        private DevExpress.XtraBars.Ribbon.RibbonPage mnuThongKe;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup1;
        private DevExpress.XtraBars.Ribbon.RibbonPage mnuTroGiup;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup7;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup8;
        private DevExpress.XtraBars.BarButtonItem barButtonItem23;
        private DevExpress.XtraTabbedMdi.XtraTabbedMdiManager MdiManager;
        private DevExpress.XtraBars.BarButtonItem barButtonItem2;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem4;
        private DevExpress.XtraBars.BarButtonItem barButtonItem3;
        private DevExpress.XtraBars.BarButtonItem btnPhanQuyen;
        private DevExpress.XtraBars.BarButtonItem barButtonItem9;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup10;
    }
}